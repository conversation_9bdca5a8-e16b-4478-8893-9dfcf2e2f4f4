import { type Page } from "@playwright/test";
import { faker } from "@faker-js/faker";
import { create, toBinary } from "@bufbuild/protobuf";
import {
  FilterSortQueryResponseSchema,
  FilterSortConfigResponseSchema,
  FilterType,
} from "@/clients/gen/broking/FilterSearch_pb";

export function mockFilterConfig(page: Page) {
  const responseData = {
    filter: {
      title: "Filter & Sort",
      items: [
        {
          key: "bond_type",
          label: "Bond Type",
          type: FilterType.MULTI_SELECT,
          options: [
            {
              label: "Corporate Bonds",
              optionValue: {
                optionValue: {
                  case: "stringValue" as const,
                  value: "corporate",
                },
              },
              isSelected: false,
            },
            {
              label: "Government Bonds",
              optionValue: {
                optionValue: {
                  case: "stringValue" as const,
                  value: "government",
                },
              },
              isSelected: false,
            },
          ],
        },
        {
          key: "ytm_range",
          label: "YTM Range",
          type: FilterType.RANGE_MULTI_SELECT,
          options: [
            {
              label: "6-8%",
              optionValue: {
                optionValue: {
                  case: "rangeValue" as const,
                  value: { lowerBound: 6, upperBound: 8 },
                },
              },
              isSelected: false,
            },
            {
              label: "8-10%",
              optionValue: {
                optionValue: {
                  case: "rangeValue" as const,
                  value: { lowerBound: 8, upperBound: 10 },
                },
              },
              isSelected: false,
            },
          ],
        },
      ],
    },
  };

  page.route("*/**/v1/filter-config", (route) => {
    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          FilterSortConfigResponseSchema,
          create(FilterSortConfigResponseSchema, responseData)
        )
      ),
    });
  });

  return responseData;
}

export function mockFilterResults(page: Page) {
  // Generate mock bond data with progressive filters
  const bondItems = Array.from({ length: 10 }, (_, index) => ({
    isin: faker.string.alphanumeric(12).toUpperCase(),
    slug: faker.helpers.slugify(faker.company.name()),
    displayTitle: faker.company.name() + " Bond",
    ytm: faker.number.float({ min: 6, max: 12, fractionDigits: 2 }),
    investabilityStatus: faker.helpers.arrayElement([
      "INVESTABLE",
      "NOT_INVESTABLE",
    ]),
    pricePerBond: faker.number.float({
      min: 1000,
      max: 10000,
      fractionDigits: 2,
    }),
    timeLeftToMaturity: `${faker.number.int({ min: 1, max: 5 })} years`,
    aboutTheInstitution: {
      bondInstitutionName: faker.company.name(),
      slug: faker.helpers.slugify(faker.company.name()),
    },
  }));

  // Generate progressive filters (suggested filters that appear between bonds)
  const suggestedFilters = {
    0: {
      key: "rating",
      label: "Credit Rating",
      type: FilterType.MULTI_SELECT,
      options: [
        {
          label: "AAA",
          optionValue: {
            optionValue: { case: "stringValue" as const, value: "AAA" },
          },
          isSelected: false,
        },
        {
          label: "AA+",
          optionValue: {
            optionValue: { case: "stringValue" as const, value: "AA+" },
          },
          isSelected: false,
        },
        {
          label: "AA",
          optionValue: {
            optionValue: { case: "stringValue" as const, value: "AA" },
          },
          isSelected: false,
        },
      ],
    },
    3: {
      key: "sector",
      label: "Sector",
      type: FilterType.MULTI_SELECT,
      options: [
        {
          label: "Banking",
          optionValue: {
            optionValue: { case: "stringValue" as const, value: "banking" },
          },
          isSelected: false,
        },
        {
          label: "Infrastructure",
          optionValue: {
            optionValue: {
              case: "stringValue" as const,
              value: "infrastructure",
            },
          },
          isSelected: false,
        },
        {
          label: "NBFC",
          optionValue: {
            optionValue: { case: "stringValue" as const, value: "nbfc" },
          },
          isSelected: false,
        },
      ],
    },
    6: {
      key: "maturity",
      label: "Time to Maturity",
      type: FilterType.RANGE_MULTI_SELECT,
      options: [
        {
          label: "1-2 years",
          optionValue: {
            optionValue: {
              case: "rangeValue" as const,
              value: { lowerBound: 1, upperBound: 2 },
            },
          },
          isSelected: false,
        },
        {
          label: "2-5 years",
          optionValue: {
            optionValue: {
              case: "rangeValue" as const,
              value: { lowerBound: 2, upperBound: 5 },
            },
          },
          isSelected: false,
        },
        {
          label: "5+ years",
          optionValue: {
            optionValue: {
              case: "rangeValue" as const,
              value: { lowerBound: 5, upperBound: 100 },
            },
          },
          isSelected: false,
        },
      ],
    },
  };

  const responseData = {
    count: 25,
    items: bondItems,
    suggestedFilters,
  };

  page.route("*/**/v1/filter-results**", (route) => {
    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          FilterSortQueryResponseSchema,
          create(FilterSortQueryResponseSchema, responseData)
        )
      ),
    });
  });

  return responseData;
}
