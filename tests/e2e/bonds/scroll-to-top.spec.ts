import { test, expect } from "@playwright/test";
import { mockFilterResults, mockFilterConfig } from "./scroll-to-top.mocks";

test.describe("Bonds Listing - Scroll to Top", () => {
  test.beforeEach(async ({ page }) => {
    // Mock filter configuration and results with progressive filters
    mockFilterConfig(page);
    mockFilterResults(page);
  });

  test("should show scroll-to-top button when scrolled past first progressive filter", async ({
    page,
  }) => {
    await page.goto("/filter-sort");

    // Wait for the page to load and sort/filter buttons to be visible
    await expect(page.getByText("SORT BY")).toBeVisible();

    // Initially, scroll-to-top button should not be visible
    const scrollToTopButton = page.getByRole("button", {
      name: "Scroll to top",
    });
    await expect(scrollToTopButton).not.toBeVisible();

    // Scroll down to make the first progressive filter go out of view
    await page.evaluate(() => {
      window.scrollTo(0, 800);
    });

    // Wait a bit for the scroll handler to process
    await page.waitForTimeout(200);

    // Now the scroll-to-top button should be visible
    await expect(scrollToTopButton).toBeVisible();

    // Click the scroll-to-top button
    await scrollToTopButton.click();

    // Wait for smooth scroll to complete
    await page.waitForTimeout(1000);

    // Verify we're back at the top (scroll-to-top button should be hidden again)
    await expect(scrollToTopButton).not.toBeVisible();

    // Verify we're actually at the top by checking scroll position
    const scrollPosition = await page.evaluate(() => window.scrollY);
    expect(scrollPosition).toBe(0);
  });

  test("should hide scroll-to-top button when scrolling back up", async ({
    page,
  }) => {
    await page.goto("/filter-sort");

    // Wait for the page to load
    await expect(page.getByText("SORT BY")).toBeVisible();

    const scrollToTopButton = page.getByRole("button", {
      name: "Scroll to top",
    });

    // Scroll down to show the button
    await page.evaluate(() => {
      window.scrollTo(0, 800);
    });
    await page.waitForTimeout(200);
    await expect(scrollToTopButton).toBeVisible();

    // Scroll back up to hide the button
    await page.evaluate(() => {
      window.scrollTo(0, 100);
    });
    await page.waitForTimeout(200);

    // Button should be hidden again
    await expect(scrollToTopButton).not.toBeVisible();
  });

  test("should have proper styling and accessibility", async ({ page }) => {
    await page.goto("/filter-sort");

    // Scroll down to show the button
    await page.evaluate(() => {
      window.scrollTo(0, 800);
    });
    await page.waitForTimeout(200);

    const scrollToTopButton = page.getByRole("button", {
      name: "Scroll to top",
    });
    await expect(scrollToTopButton).toBeVisible();

    // Check accessibility attributes
    await expect(scrollToTopButton).toHaveAttribute(
      "aria-label",
      "Scroll to top"
    );

    // Check styling classes (basic verification)
    const buttonClasses = await scrollToTopButton.getAttribute("class");
    expect(buttonClasses).toContain("fixed");
    expect(buttonClasses).toContain("rounded-full");
    expect(buttonClasses).toContain("bg-black-80");

    // Check that the button contains the rotated caret icon
    const caretIcon = scrollToTopButton.locator("svg");
    await expect(caretIcon).toBeVisible();
    const iconClasses = await caretIcon.getAttribute("class");
    expect(iconClasses).toContain("rotate-180");
  });
});
